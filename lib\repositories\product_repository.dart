/// Blue Booth Manager - 상품 데이터 Repository
///
/// 상품 데이터의 CRUD, 정렬/필터, 재고/할인/이미지 등 관리 기능을 제공하는 Repository 클래스입니다.
/// - 로컬 DB(SQLite)와 연동하며, 오프라인 작업 큐/동기화 구조를 지원합니다.
/// - 모든 메서드는 SQL injection 방지, 네트워크 상태 체크 등 보안/안정성 고려.
///
/// 주요 기능:
/// - 상품 CRUD (생성, 조회, 수정, 삭제)
/// - 정렬 및 필터링 (이름, 수량, 가격, 판매자, 최근등록)
/// - 검색 기능 (이름, 판매자별)
/// - 재고 관리 (수량 업데이트, 재고 부족 감지)
/// - 오프라인 지원 (작업 큐 저장, 동기화)
/// - 페이지네이션 (대용량 데이터 처리)
///
/// 보안 기능:
/// - SQL injection 방지 (SqlUtils 사용)
/// - 허용된 컬럼만 정렬 가능
/// - 입력값 검증 및 이스케이프 처리
/// - 네트워크 상태에 따른 오프라인 큐 저장
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 6월
library;

import 'dart:async';
import 'package:sqflite/sqflite.dart';
import '../models/product.dart';
import '../models/product_sort_option.dart';
import '../services/database_service.dart';
import '../services/event_workspace_manager.dart';
import '../utils/sql_utils.dart';
import '../utils/batch_processor.dart';
import '../utils/logger_utils.dart';

/// 상품 데이터의 CRUD, 정렬/필터, 재고/할인/이미지 등 관리 기능을 제공하는 Repository 클래스입니다.
/// - 로컬 DB(SQLite)와 연동하며, 오프라인 작업 큐/동기화 구조를 지원합니다.
/// - 모든 메서드는 SQL injection 방지, 네트워크 상태 체크 등 보안/안정성 고려.
///
/// 아키텍처 특징:
/// - Repository 패턴으로 데이터 접근 추상화
/// - 오프라인 우선 전략 (Offline-First)
/// - 보안 우선 설계 (SQL injection 방지)
/// - 성능 최적화 (인덱스, 배치 처리)
class ProductRepository {
  /// 데이터베이스 서비스 인스턴스
  final DatabaseService _databaseService;

  /// 데이터베이스 연결 (지연 초기화)
  late final Future<Database> _database;

  /// 허용된 정렬 컬럼 목록 (SQL injection 방지)
  ///
  /// 정렬 시 사용할 수 있는 컬럼만 허용하여 보안을 강화합니다.
  /// 새로운 컬럼 추가 시 이 목록에 추가해야 합니다.
  static const List<String> _allowedOrderByColumns = [
    'name', // 상품명
    'quantity', // 수량
    'price', // 가격
    'sellername', // 판매자명
    'id', // 고유번호 (최근등록용)
  ];

  /// Repository 생성자
  ///
  /// [database]: 데이터베이스 서비스 인스턴스
  ///
  /// 초기화:
  /// - 데이터베이스 연결 설정
  /// - 지연 초기화로 성능 최적화
  ProductRepository({required DatabaseService database})
    : _databaseService = database {
    _database = _databaseService.database;
  }

  /// 모든 상품 목록을 이름 오름차순으로 조회합니다.
  ///
  /// 기본 조회 메서드로, 상품 관리 화면에서 사용됩니다.
  ///
  /// 반환값: Product 리스트 (빈 리스트 가능)
  /// 예외: DB 연결/쿼리 실패 시 Exception
  ///
  /// 성능 고려사항:
  /// - 인덱스 활용 (name 컬럼)
  /// - 메모리 효율적 처리
  /// - 쿼리 최적화
  Future<List<Product>> getAllProducts() async {
    final stopwatch = Stopwatch()..start();

    final db = await _database;

    // 현재 워크스페이스의 eventId로 스코프 강제
    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final where = currentEventId != null ? 'eventId = ?' : null;
    final whereArgs = currentEventId != null ? [currentEventId] : null;

    // 성능 최적화: 필요한 컬럼만 선택하고 인덱스 활용
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      columns: ['id', 'name', 'quantity', 'price', 'imagePath', 'sellerName', 'lastServicedDate', 'isActive', 'focusX', 'focusY', 'eventId', 'categoryId'], // categoryId 추가!
      where: where,
      whereArgs: whereArgs,
      orderBy: 'name ASC', // 기본 정렬: 이름 오름차순 (인덱스 활용)
    );

    final products = maps.map((map) => Product.fromMap(map)).toList();

    stopwatch.stop();
    LoggerUtils.logInfo('상품 조회 완료: ${products.length}개, 소요시간: ${stopwatch.elapsedMilliseconds}ms, eventScoped=${currentEventId != null}', tag: 'ProductRepository');

    return products;
  }

  /// 특정 행사의 상품 목록을 페이지네이션으로 조회합니다.
  ///
  /// [eventId]: 조회할 행사의 ID
  /// [limit]: 한 페이지당 상품 수 (기본값: 50)
  /// [offset]: 건너뛸 상품 수 (기본값: 0)
  /// 반환값: 해당 행사의 Product 리스트
  /// 예외: DB 오류 시 Exception
  ///
  /// 성능 고려사항:
  /// - eventId 인덱스 활용
  /// - 메모리 효율적 처리 (페이지네이션)
  /// - 쿼리 최적화
  Future<List<Product>> getProductsByEventId(int eventId, {int limit = 50, int offset = 0}) async {
    final stopwatch = Stopwatch()..start();

    final db = await _database;

    // 성능 최적화: eventId 인덱스 활용 + 페이지네이션
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      columns: ['id', 'name', 'quantity', 'price', 'imagePath', 'sellerName', 'lastServicedDate', 'isActive', 'focusX', 'focusY', 'eventId', 'categoryId'], // categoryId 추가!
      where: 'eventId = ?',
      whereArgs: [eventId],
      orderBy: 'name ASC', // 기본 정렬: 이름 오름차순
      limit: limit,
      offset: offset,
    );

    final products = maps.map((map) => Product.fromMap(map)).toList();

    stopwatch.stop();
    LoggerUtils.logInfo('행사별 상품 조회 완료: eventId=$eventId, ${products.length}개, 소요시간: ${stopwatch.elapsedMilliseconds}ms', tag: 'ProductRepository');

    return products;
  }

  /// 특정 행사의 전체 상품 수를 조회합니다.
  ///
  /// [eventId]: 조회할 행사의 ID
  /// 반환값: 해당 행사의 전체 상품 수
  /// 예외: DB 오류 시 Exception
  ///
  /// 페이지네이션에서 전체 페이지 수 계산에 사용됩니다.
  Future<int> getProductCountByEventId(int eventId) async {
    final db = await _database;

    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseServiceImpl.productsTable} WHERE eventId = ?',
      [eventId],
    );

    return result.first['count'] as int;
  }

  /// 정렬 옵션에 따라 상품 목록을 조회합니다.
  ///
  /// 다양한 정렬 기준을 지원하여 사용자 경험을 향상시킵니다.
  /// SQL injection 방지를 위해 허용된 컬럼만 사용합니다.
  ///
  /// [sortOption]: ProductSortOption (이름/수량/가격/판매자/최근등록 등)
  /// 반환값: 정렬된 Product 리스트
  /// 예외: 잘못된 정렬 옵션/DB 오류 시 Exception
  ///
  /// 보안 기능:
  /// - SqlUtils.createSafeOrderBy로 SQL injection 방지
  /// - 허용된 컬럼만 정렬 가능
  /// - 정렬 방향 검증
  Future<List<Product>> getProductsSorted(ProductSortOption sortOption) async {
    String orderBy;

    // 정렬 옵션에 따른 안전한 ORDER BY 절 생성
    switch (sortOption) {
      case ProductSortOption.nameAsc:
        orderBy = SqlUtils.createSafeOrderBy('name', _allowedOrderByColumns);
        break;
      case ProductSortOption.nameDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'name',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
      case ProductSortOption.quantityAsc:
        orderBy = SqlUtils.createSafeOrderBy(
          'quantity',
          _allowedOrderByColumns,
        );
        break;
      case ProductSortOption.quantityDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'quantity',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
      case ProductSortOption.priceAsc:
        orderBy = SqlUtils.createSafeOrderBy('price', _allowedOrderByColumns);
        break;
      case ProductSortOption.priceDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'price',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
      case ProductSortOption.sellerAsc:
        orderBy = SqlUtils.createSafeOrderBy(
          'sellerName',
          _allowedOrderByColumns,
        );
        break;
      case ProductSortOption.sellerDesc:
        orderBy = SqlUtils.createSafeOrderBy(
          'sellerName',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
      case ProductSortOption.recentlyAdded:
        orderBy = SqlUtils.createSafeOrderBy(
          'id',
          _allowedOrderByColumns,
          descending: true,
        );
        break;
    }

    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      orderBy: orderBy,
    );
    return maps.map((map) => Product.fromMap(map)).toList();
  }

  /// 상품 ID로 특정 상품을 조회합니다.
  ///
  /// 상품 상세 정보 조회나 수정 시 사용됩니다.
  ///
  /// [id]: 상품 고유번호 (양의 정수)
  /// 반환값: Product 또는 null(없을 경우)
  /// 예외: DB 오류 시 Exception
  ///
  /// 성능 최적화:
  /// - PRIMARY KEY 인덱스 활용
  /// - 단일 레코드 조회로 빠른 응답
  Future<Product?> getProductById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      where: 'id = ?', // 파라미터화된 쿼리로 SQL injection 방지
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }
    return null;
  }

  /// 모든 판매자 이름 목록을 중복 없이 조회합니다.
  ///
  /// 필터링이나 통계에서 사용되는 판매자 목록을 제공합니다.
  ///
  /// 반환값: 판매자 이름(String) 리스트 (빈 문자열 제외)
  /// 예외: DB 오류 시 Exception
  ///
  /// 데이터 정제:
  /// - DISTINCT로 중복 제거
  /// - NULL 및 빈 문자열 제외
  /// - 알파벳 순 정렬
  Future<List<String>> getAllSellerNames() async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['sellerName IS NOT NULL AND sellerName != ""'];
    final whereArgs = <Object>[];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      columns: ['DISTINCT sellerName'], // 중복 제거
      where: whereParts.join(' AND '), // 유효한 데이터만 (+ eventId)
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'sellerName ASC', // 알파벳 순 정렬
    );

    return maps
        .map((map) => map['sellerName'] as String)
        .where((name) => name.isNotEmpty) // 추가 필터링
        .toList();
  }

  /// 특정 판매자별 상품 목록을 조회합니다.
  ///
  /// 판매자별 상품 관리나 통계에서 사용됩니다.
  ///
  /// [sellerName]: 판매자 이름 (정확히 일치하는 경우만)
  /// 반환값: 해당 판매자의 Product 리스트
  /// 예외: DB 오류 시 Exception
  ///
  /// 보안 기능:
  /// - 파라미터화된 쿼리로 SQL injection 방지
  /// - 정확한 매칭으로 데이터 무결성 보장
  Future<List<Product>> getProductsBySeller(String sellerName) async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['sellerName = ?'];
    final whereArgs = <Object>[sellerName];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      where: whereParts.join(' AND '), // 파라미터화된 쿼리 + eventId 스코프
      whereArgs: whereArgs,
      orderBy: 'name ASC', // 상품명 순 정렬
    );
    return maps.map((map) => Product.fromMap(map)).toList();
  }

  /// 상품을 신규 등록합니다.
  ///
  /// 오프라인 지원을 포함한 상품 등록 기능입니다.
  /// 네트워크 상태에 따라 즉시 저장하거나 오프라인 큐에 저장합니다.
  ///
  /// [product]: 등록할 Product 객체 (ID는 자동 생성)
  /// 반환값: 신규 row의 id(오프라인 큐 저장 시 -1)
  /// 예외: DB 오류/네트워크 상태 등
  ///
  /// 오프라인 지원:
  /// - 네트워크 없을 때 오프라인 큐에 저장
  /// - 온라인 전환 시 자동 동기화
  /// - 데이터 무결성 보장
  Future<int> insertProduct(Product product) async {
    final db = await _database;

    LoggerUtils.logInfo('상품 DB 삽입 시작 - 이름: ${product.name}, eventId: ${product.eventId}, categoryId: ${product.categoryId}', tag: 'ProductRepository');

    // 1. 같은 카테고리 내에서 상품명 중복 체크
    final List<Map<String, dynamic>> existingProducts = await db.query(
      DatabaseServiceImpl.productsTable,
      where: 'name = ? AND categoryId = ? AND eventId = ?',
      whereArgs: [product.name, product.categoryId, product.eventId],
    );

    if (existingProducts.isNotEmpty) {
      LoggerUtils.logWarning('상품명 중복 - 카테고리 ID ${product.categoryId}에 이미 "${product.name}" 상품이 존재합니다.', tag: 'ProductRepository');
      throw Exception('같은 카테고리 안에서는 상품명이 중복될 수 없습니다.');
    }

    // 2. 로컬 DB에 저장
    final insertedId = await db.insert(
      DatabaseServiceImpl.productsTable,
      product.toMap(),
    );

    LoggerUtils.logInfo('상품 DB 삽입 성공 - ID: $insertedId, 이름: ${product.name}', tag: 'ProductRepository');
    
    // 3. 실시간 동기화는 ProductNotifier에서 직접 처리하므로 여기서는 하지 않음
    // _syncProductInBackground(productWithId, 'add'); // 무한 루프 방지를 위해 비활성화
    
    return insertedId;
  }

  /// 상품 정보를 수정합니다.
  ///
  /// 오프라인 지원을 포함한 상품 수정 기능입니다.
  /// 판매자가 변경된 경우 연관된 판매 기록도 함께 업데이트합니다.
  ///
  /// [product]: 수정할 Product 객체 (ID 필수)
  /// [updateRelatedSalesLogs]: 판매자 변경 시 연관 판매 기록 업데이트 여부 (기본값: true)
  /// 반환값: 수정된 row 개수(오프라인 큐 저장 시 -1)
  /// 예외: DB 오류/네트워크 상태 등
  ///
  /// 데이터 무결성:
  /// - ID 기반 정확한 레코드 수정
  /// - 판매자 변경 시 연관 기록 동기화
  /// - 오프라인 시에도 수정 정보 보존
  Future<int> updateProduct(Product product, {bool updateRelatedSalesLogs = true}) async {
    final db = await _database;
    
    if (product.id == null) {
      throw Exception('상품 ID가 null입니다. 업데이트할 수 없습니다.');
    }

    // 1. 같은 카테고리 내에서 상품명 중복 체크 (자기 자신 제외)
    final List<Map<String, dynamic>> existingProducts = await db.query(
      DatabaseServiceImpl.productsTable,
      where: 'name = ? AND categoryId = ? AND eventId = ? AND id != ?',
      whereArgs: [product.name, product.categoryId, product.eventId, product.id],
    );

    if (existingProducts.isNotEmpty) {
      LoggerUtils.logWarning('상품명 중복 - 카테고리 ID ${product.categoryId}에 이미 "${product.name}" 상품이 존재합니다.', tag: 'ProductRepository');
      throw Exception('같은 카테고리 안에서는 상품명이 중복될 수 없습니다.');
    }

    // 2. 판매자 변경 또는 상품명 변경 시 연관 기록 업데이트 체크
    int updatedRows = 0;
    if (updateRelatedSalesLogs) {
      final existingProduct = await getProductById(product.id!);
      if (existingProduct != null) {
        final sellerChanged = existingProduct.sellerName != product.sellerName && product.sellerName != null;
        final nameChanged = existingProduct.name != product.name;

        if (sellerChanged || nameChanged) {
          await db.transaction((txn) async {
            // 상품 정보 업데이트
            await txn.update(
              DatabaseServiceImpl.productsTable,
              product.toMap(),
              where: 'id = ?',
              whereArgs: [product.id!],
            );

            // 연관된 판매 기록 업데이트
            final updateData = <String, dynamic>{};
            if (sellerChanged) {
              updateData['sellerName'] = product.sellerName;
            }
            if (nameChanged) {
              updateData['productName'] = product.name;
            }

            if (updateData.isNotEmpty) {
              await txn.update(
                DatabaseServiceImpl.salesLogTable,
                updateData,
                where: 'productId = ?',
                whereArgs: [product.id!],
              );
            }
          });
          updatedRows = 1;
        }
      }
    }
    
    // 3. 일반 업데이트
    if (updatedRows == 0) {
      updatedRows = await db.update(
        DatabaseServiceImpl.productsTable,
        product.toMap(),
        where: 'id = ?',
        whereArgs: [product.id!],
      );
    }
    
    // 4. 실시간 동기화는 ProductNotifier에서 직접 처리하므로 여기서는 하지 않음
    // _syncProductInBackground(product, 'update'); // 무한 루프 방지를 위해 비활성화
    return updatedRows;
  }

  /// 여러 상품 정보를 일괄 수정합니다.
  ///
  /// 대량 데이터 처리 시 성능을 최적화합니다.
  ///
  /// [products]: 수정할 Product 리스트
  /// 반환값: 없음
  /// 예외: DB 오류 시 Exception
  ///
  /// 성능 최적화:
  /// - 배치 처리로 트랜잭션 효율성 향상
  /// - 메모리 사용량 최적화
  /// - 원자성 보장
  Future<void> updateMultipleProducts(List<Product> products) async {
    final db = await _database;
    final batch = db.batch();

    for (Product product in products) {
      if (product.id == null) {
        throw Exception('상품 ID가 null입니다. 업데이트할 수 없습니다.');
      }
      batch.update(
        DatabaseServiceImpl.productsTable,
        product.toMap(),
        where: 'id = ?',
        whereArgs: [product.id!],
      );
    }

    await batch.commit();
  }

  /// 상품을 삭제합니다.
  ///
  /// [product]: 삭제할 Product 객체
  /// 반환값: 삭제된 row 개수(오프라인 큐 저장 시에도 row 개수 반환)
  /// 예외: DB 오류/네트워크 상태 등
  Future<int> deleteProduct(Product product) async {
    final db = await _database;
    if (product.id == null) {
      throw Exception('상품 ID가 null입니다. 삭제할 수 없습니다.');
    }

    LoggerUtils.logInfo('[삭제 시작] 상품 id=${product.id}, name=${product.name}, eventId=${product.eventId}', tag: 'ProductRepository');

    // 1. 삭제 전 상품 존재 여부 확인
    final existingProduct = await db.query(
      DatabaseServiceImpl.productsTable,
      where: 'id = ?',
      whereArgs: [product.id!],
    );

    if (existingProduct.isEmpty) {
      LoggerUtils.logWarning('[삭제 실패] 상품이 이미 존재하지 않습니다: id=${product.id}', tag: 'ProductRepository');
      return 0; // 이미 삭제된 상품
    }

    // 2. 로컬 DB에서 삭제
    final deletedRows = await db.delete(
      DatabaseServiceImpl.productsTable,
      where: 'id = ?',
      whereArgs: [product.id!],
    );

    // 3. 실시간 동기화는 ProductNotifier에서 직접 처리하므로 여기서는 하지 않음
    // _syncProductInBackground(product, 'delete'); // 무한 루프 방지를 위해 비활성화

    LoggerUtils.logInfo('[삭제 완료] 상품 id=${product.id}, deletedRows=$deletedRows', tag: 'ProductRepository');
    return deletedRows;
  }

  /// 상품 재고 수량을 증감합니다. (원자적 연산으로 Race Condition 방지)
  ///
  /// [productId]: 상품 ID
  /// [changeInQuantity]: 증감 수량(음수 가능)
  /// 반환값: 수정된 row 개수(0=실패)
  /// 예외: DB 오류/수량 음수 등
  Future<int> updateStock(int productId, int changeInQuantity) async {
    final db = await _database;

    // 원자적 연산으로 Race Condition 방지하고 Firebase 중복 업로드 방지
    return await db.transaction((txn) async {
      // 1. 현재 재고 확인 (트랜잭션 내에서)
      final result = await txn.query(
        DatabaseServiceImpl.productsTable,
        columns: ['quantity'],
        where: 'id = ?',
        whereArgs: [productId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw Exception('상품을 찾을 수 없습니다.');
      }

      final currentQuantity = result.first['quantity'] as int;
      final newQuantity = currentQuantity + changeInQuantity;

      if (newQuantity < 0) {
        throw Exception('재고가 부족합니다. (현재: $currentQuantity, 요청: ${changeInQuantity.abs()})');
      }

      // 2. 재고 업데이트 (트랜잭션 내에서, Firebase 중복 업로드 방지)
      return await txn.update(
        DatabaseServiceImpl.productsTable,
        {'quantity': newQuantity},
        where: 'id = ? AND quantity = ?',  // 현재 수량도 확인하여 이중 체크
        whereArgs: [productId, currentQuantity],
      );
    });
  }

  /// 상품 재고 수량을 지정된 값으로 설정합니다. (원자적 연산으로 Race Condition 방지)
  ///
  /// [productId]: 상품 ID
  /// [newQuantity]: 새 재고 수량
  /// 반환값: 수정된 row 개수(0=실패)
  /// 예외: DB 오류/음수 등
  Future<int> setStock(int productId, int newQuantity) async {
    final db = await _database;
    
    if (newQuantity < 0) {
      throw Exception('재고는 음수가 될 수 없습니다.');
    }

    // 원자적 연산으로 Race Condition 방지하고 Firebase 중복 업로드 방지
    return await db.transaction((txn) async {
      // 1. 상품 존재 확인 (트랜잭션 내에서)
      final result = await txn.query(
        DatabaseServiceImpl.productsTable,
        columns: ['id'],
        where: 'id = ?',
        whereArgs: [productId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw Exception('상품을 찾을 수 없습니다.');
      }

      // 2. 재고 직접 업데이트 (Firebase 중복 업로드 방지)
      return await txn.update(
        DatabaseServiceImpl.productsTable,
        {'quantity': newQuantity},
        where: 'id = ?',
        whereArgs: [productId],
      );
    });
  }

  /// 상품 활성 상태를 토글합니다. (원자적 연산으로 Firebase 중복 업로드 방지)
  Future<void> toggleProductActive(int productId) async {
    final db = await _database;
    
    // 원자적 연산으로 Race Condition 방지하고 Firebase 중복 업로드 방지
    await db.transaction((txn) async {
      // 1. 현재 활성 상태 확인 (트랜잭션 내에서)
      final result = await txn.query(
        DatabaseServiceImpl.productsTable,
        columns: ['isActive'],
        where: 'id = ?',
        whereArgs: [productId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw Exception('상품을 찾을 수 없습니다.');
      }

      final currentActive = (result.first['isActive'] as int) == 1;
      final newActive = !currentActive;

      // 2. 활성 상태 직접 업데이트 (Firebase 중복 업로드 방지)
      await txn.update(
        DatabaseServiceImpl.productsTable,
        {'isActive': newActive ? 1 : 0},
        where: 'id = ?',
        whereArgs: [productId],
      );
    });
  }

  /// 상품 이미지 경로를 수정합니다. (원자적 연산으로 Firebase 중복 업로드 방지)
  Future<void> updateProductImage(int productId, String imagePath) async {
    final db = await _database;
    
    // 원자적 연산으로 Race Condition 방지하고 Firebase 중복 업로드 방지
    await db.transaction((txn) async {
      // 1. 상품 존재 확인 (트랜잭션 내에서)
      final result = await txn.query(
        DatabaseServiceImpl.productsTable,
        columns: ['id'],
        where: 'id = ?',
        whereArgs: [productId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw Exception('상품을 찾을 수 없습니다.');
      }

      // 2. 이미지 경로 직접 업데이트 (Firebase 중복 업로드 방지)
      await txn.update(
        DatabaseServiceImpl.productsTable,
        {'imagePath': imagePath},
        where: 'id = ?',
        whereArgs: [productId],
      );
    });
  }



  /// 모든 상품 삭제
  Future<int> deleteAllProducts() async {
    final db = await _database;
    return await db.delete(DatabaseServiceImpl.productsTable);
  }

  /// 활성 상품만 조회
  Future<List<Product>> getActiveProducts() async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['isActive = ?'];
    final whereArgs = <Object>[1];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'name ASC',
    );
    return maps.map((map) => Product.fromMap(map)).toList();
  }

  /// 재고 있는 상품만 조회
  Future<List<Product>> getProductsInStock() async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['quantity > 0'];
    final whereArgs = <Object>[];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'name ASC',
    );
    return maps.map((map) => Product.fromMap(map)).toList();
  }

  /// 검색 기능
  Future<List<Product>> searchProducts(String searchQuery) async {
    final db = await _database;

    final currentEventId = EventWorkspaceManager.instance.currentWorkspace?.id;
    final whereParts = <String>['(name LIKE ? OR sellerName LIKE ?)'];
    final whereArgs = <Object>['%$searchQuery%', '%$searchQuery%'];
    if (currentEventId != null) {
      whereParts.add('eventId = ?');
      whereArgs.add(currentEventId);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.productsTable,
      where: whereParts.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'name ASC',
    );
    return maps.map((map) => Product.fromMap(map)).toList();
  }

  /// 재고 수량 직접 설정
  Future<int> updateProductStock(int productId, int newQuantity) async {
    return await setStock(productId, newQuantity);
  }

  /// 재고 수량 증가 (원자적 연산으로 Firebase 중복 업로드 방지)
  Future<int> increaseStock(int productId, int quantity) async {
    final db = await _database;

    // 원자적 연산으로 Race Condition 방지하고 Firebase 중복 업로드 방지
    return await db.transaction((txn) async {
      // 1. 현재 재고 확인 (트랜잭션 내에서)
      final result = await txn.query(
        DatabaseServiceImpl.productsTable,
        columns: ['quantity'],
        where: 'id = ?',
        whereArgs: [productId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw Exception('상품을 찾을 수 없습니다.');
      }

      final currentQuantity = result.first['quantity'] as int;
      final newQuantity = currentQuantity + quantity;

      // 2. 재고 증가 (트랜잭션 내에서, Firebase 중복 업로드 방지)
      return await txn.update(
        DatabaseServiceImpl.productsTable,
        {'quantity': newQuantity},
        where: 'id = ? AND quantity = ?',  // 현재 수량도 확인하여 이중 체크
        whereArgs: [productId, currentQuantity],
      );
    });
  }

  /// 상품 재고를 감소시킵니다. (원자적 연산으로 Race Condition 방지)
  ///
  /// [productId]: 상품 ID
  /// [quantity]: 감소시킬 수량
  /// 반환값: 업데이트된 행 수
  /// 예외: DB 오류 시 Exception
  Future<int> decreaseStock(int productId, int quantity) async {
    final db = await _database;

    // 원자적 연산으로 Race Condition 방지
    return await db.transaction((txn) async {
      // 1. 현재 재고 확인 (트랜잭션 내에서)
      final result = await txn.query(
        DatabaseServiceImpl.productsTable,
        columns: ['quantity'],
        where: 'id = ?',
        whereArgs: [productId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw Exception('상품을 찾을 수 없습니다.');
      }

      final currentQuantity = result.first['quantity'] as int;
      final newQuantity = currentQuantity - quantity;

      if (newQuantity < 0) {
        throw Exception('재고가 부족합니다. (현재: $currentQuantity, 요청: $quantity)');
      }

      // 2. 재고 업데이트 (트랜잭션 내에서)
      return await txn.update(
        DatabaseServiceImpl.productsTable,
        {'quantity': newQuantity},
        where: 'id = ? AND quantity = ?',  // 현재 수량도 확인하여 이중 체크
        whereArgs: [productId, currentQuantity],
      );
    });
  }

  /// ========== 배치 처리 메서드들 ==========

  /// 대량 상품 등록 (배치 처리)
  Future<BatchResult<Product>> batchInsertProducts(List<Product> products) async {
    final processor = AdvancedBatchProcessor<Product>(
      items: products,
      processBatch: (batch) async {
        final db = await _database;
        await db.transaction((txn) async {
          for (final product in batch) {
            await txn.insert(
              DatabaseServiceImpl.productsTable,
              product.toMap(),
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
        });
      },
    );
    return await processor.process();
  }

  /// 대량 상품 수정 (배치 처리)
  Future<BatchResult<Product>> batchUpdateProducts(List<Product> products) async {
    final processor = AdvancedBatchProcessor<Product>(
      items: products,
      processBatch: (batch) async {
        final db = await _database;
        await db.transaction((txn) async {
          for (final product in batch) {
            await txn.update(
              DatabaseServiceImpl.productsTable,
              product.toMap(),
              where: 'id = ?',
              whereArgs: [product.id],
            );
          }
        });
      },
    );
    return await processor.process();
  }

  /// 대량 상품 삭제 (배치 처리)
  Future<BatchResult<int>> batchDeleteProducts(List<int> productIds) async {
    final processor = AdvancedBatchProcessor<int>(
      items: productIds,
      processBatch: (batch) async {
        final db = await _database;
        await db.transaction((txn) async {
          for (final id in batch) {
            await txn.delete(
              DatabaseServiceImpl.productsTable,
              where: 'id = ?',
              whereArgs: [id],
            );
          }
        });
      },
    );
    return await processor.process();
  }

  /// ========== 실시간 동기화 메서드들 ==========
  /// 
  /// 실시간 동기화는 현재 ProductNotifier에서 직접 Firestore를 구독하여 처리하므로
  /// Repository 레벨에서의 자동 동기화는 무한 루프 방지를 위해 비활성화됨

  // 기존 BatchJob, addJob, getJob, getResult 등 관련 메서드/필드 제거
}
