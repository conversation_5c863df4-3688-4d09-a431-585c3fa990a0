import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/payment_methods_provider.dart';

class PaymentMethodsDialog extends ConsumerWidget {
  const PaymentMethodsDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(paymentMethodsProvider);
    final notifier = ref.read(paymentMethodsProvider.notifier);



    return AlertDialog(
      title: const Text('결제수단'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '사용할 결제수단을 선택하세요 (최소 1개)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            for (final method in state.methods)
              CheckboxListTile(
                title: Text(method.name),
                value: method.isActive,
                onChanged: (bool? value) {
                  if (value != null) {
                    notifier.toggleMethod(method.id);
                  }
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                '최소 1개, 최대 3개',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('닫기'),
        ),
      ],
    );
  }
}

