import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class RegistrationCompletePage extends StatelessWidget {
  final String title;
  final String? description;
  final VoidCallback? onConfirm;
  final VoidCallback? onClose;

  const RegistrationCompletePage({
    super.key,
    this.title = '등록이 완료되었습니다!',
    this.description,
    this.onConfirm,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Center(
                child: Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: isTablet ? 48.0 : 24.0,
                    vertical: isTablet ? 32.0 : 20.0,
                  ),
                  padding: EdgeInsets.all(isTablet ? 32.0 : 24.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.surface,
                        AppColors.surfaceVariant,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadow,
                        blurRadius: 24,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 성공 아이콘 (더 컴팩트)
                      Container(
                        padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.success,
                              AppColors.successLight,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(40),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.success.withValues(alpha: 0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.check_circle_rounded,
                          color: AppColors.onboardingTextOnPrimary,
                          size: isTablet ? 80.0 : 64.0,
                        ),
                      ),
                      SizedBox(height: isTablet ? 24.0 : 20.0),

                      // 제목
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: isTablet ? 24.0 : 20.0,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onboardingTextPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      // 설명
                      if (description != null) ...[
                        SizedBox(height: isTablet ? 16.0 : 12.0),
                        Text(
                          description!,
                          style: TextStyle(
                            fontSize: isTablet ? 16.0 : 14.0,
                            color: AppColors.onboardingTextSecondary,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                      SizedBox(height: isTablet ? 32.0 : 28.0),

                      // 확인 버튼
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: AppColors.primaryGradient,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.onboardingPrimary.withValues(alpha: 0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(16),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(16),
                            onTap: onConfirm ?? () => Navigator.of(context).pop(),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                vertical: isTablet ? 24.0 : 18.0,
                                horizontal: isTablet ? 32.0 : 24.0,
                              ),
                              child: Text(
                                '확인',
                                style: TextStyle(
                                  fontSize: isTablet ? 20.0 : 18.0,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onboardingTextOnPrimary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 닫기 버튼
              Positioned(
                top: isTablet ? 16.0 : 8.0,
                right: isTablet ? 16.0 : 8.0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.surface,
                        AppColors.surfaceVariant,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowLight,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: onClose ?? () => Navigator.of(context).pop(),
                      child: Container(
                        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
                        child: Icon(
                          Icons.close_rounded,
                          color: AppColors.onboardingTextSecondary,
                          size: isTablet ? 32.0 : 24.0,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 