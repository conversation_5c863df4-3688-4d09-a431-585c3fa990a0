import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../models/event_workspace.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import 'post_onboarding_init_screen.dart';
import '../../utils/app_colors.dart';
import '../../utils/orientation_helper.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/onboarding_components.dart';
import '../../widgets/image_crop_widget.dart';
import '../../utils/image_utils.dart';

/// 온보딩 전용 행사 워크스페이스 등록 화면 - 웜톤 디자인으로 개선
///
/// 웜톤 색상과 개선된 폼 디자인을 적용한 현대적 행사 워크스페이스 등록 경험
/// 반응형 레이아웃으로 모든 기기에서 최적화된 경험 제공
class EventWorkspaceOnboardingScreen extends ConsumerStatefulWidget {
  final VoidCallback onWorkspaceCreated;

  const EventWorkspaceOnboardingScreen({
    super.key,
    required this.onWorkspaceCreated,
  });

  @override
  ConsumerState<EventWorkspaceOnboardingScreen> createState() => _EventWorkspaceOnboardingScreenState();
}

class _EventWorkspaceOnboardingScreenState extends ConsumerState<EventWorkspaceOnboardingScreen>
    with TickerProviderStateMixin {
  static const String _tag = 'EventWorkspaceOnboardingScreen';

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _memoController = TextEditingController();
  
  DateTime? _startDate;
  DateTime? _endDate;
  String? _imagePath;
  bool _isLoading = false;
  bool _isInitializing = false; // 초기화 중 상태

  // 애니메이션 컨트롤러
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);

    // 세로모드로 고정
    OrientationHelper.enterPortraitMode();

    // 애니메이션 초기화
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));
    
    // 기본 날짜 설정 제거 - 사용자가 직접 선택하도록 변경
    
    // 애니메이션 시작
    _fadeController.forward();
    _slideController.forward();
    
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _memoController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(context),
            ),
          ),
        ),
      ),
    );
  }

  /// 메인 바디 구성
  Widget _buildBody(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(color: AppColors.onboardingPrimary),
            const SizedBox(height: 16),
            Text(
              '행사를 생성하고 있습니다...',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (_isInitializing) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(color: AppColors.onboardingPrimary),
            const SizedBox(height: 16),
            Text(
              '데이터를 초기화하고 있습니다...',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '잠시만 기다려주세요',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return Center(
      child: SingleChildScrollView(
        child: OnboardingComponents.buildCard(
          context: context,
          child: _buildEventForm(context),
        ),
      ),
    );
  }

  /// 행사 등록 폼 구성
  Widget _buildEventForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 헤더
          _buildHeader(context),

          OnboardingComponents.buildSectionSpacing(context),

          // 이미지와 입력 필드가 나란히 배치되는 섹션
          _buildImageAndInputSection(context),

          OnboardingComponents.buildSectionSpacing(context),

          // 날짜 선택 섹션
          _buildDateSection(context),

          OnboardingComponents.buildSectionSpacing(context),

          // 생성 버튼
          OnboardingComponents.buildPrimaryButton(
            context: context,
            text: '행사 생성하기',
            onPressed: _canCreateEvent() ? _createEvent : null,
            isLoading: _isLoading,
            icon: Icons.add_business,
          ),

          OnboardingComponents.buildSmallSpacing(context),

          // 도움말 텍스트
          _buildHelpText(context),
        ],
      ),
    );
  }

  /// 헤더 섹션
  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: '첫 행사를 등록해보세요',
        ),

        const SizedBox(height: 6),

        // 부제목
        OnboardingComponents.buildSubtitle(
          context: context,
          text: '파라바라를 시작할 준비가 끝났습니다.',
        ),
      ],
    );
  }

  /// 이미지와 입력 필드 섹션 (나란히 배치)
  Widget _buildImageAndInputSection(BuildContext center) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 왼쪽: 이미지 추가
        _buildImageCropSection(context),
        
        const SizedBox(width: 16),
        
        // 오른쪽: 행사명과 메모 입력
        Expanded(
          child: Column(
            children: [
              // 행사명 입력
              OnboardingComponents.buildTextField(
                context: context,
                controller: _nameController,
                label: '행사명',
                hint: '행사 이름을 입력하세요',
                prefixIcon: Icons.event,
                textInputAction: TextInputAction.next,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '행사명을 입력해주세요';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 12),
              
              // 메모 입력
              OnboardingComponents.buildTextField(
                context: context,
                controller: _memoController,
                label: '메모',
                hint: '행사에 대한 메모를 입력하세요',
                prefixIcon: Icons.note_add,
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 이미지 크롭 섹션 (왼쪽 배치용)
  Widget _buildImageCropSection(BuildContext context) {
    return GestureDetector(
      onTap: _pickAndCropImage,
      child: Container(
        width: 110,
        height: 110,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: _imagePath != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.file(
                  File(_imagePath!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildImagePlaceholder();
                  },
                ),
              )
            : _buildImagePlaceholder(),
      ),
    );
  }

  /// 이미지 플레이스홀더 (작은 크기에 맞게 조정)
  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_photo_alternate_outlined,
          size: 32,
          color: AppColors.onboardingPrimary,
        ),
        const SizedBox(height: 4),
        Text(
          '이미지 추가',
          style: TextStyle(
            fontSize: 10,
            color: AppColors.onboardingPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 이미지 선택 및 크롭
  Future<void> _pickAndCropImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // 1. 3단계 방식: 흰색 800x800 캔버스 + 이미지 최대 650px로 전처리
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await Directory.systemTemp.createTemp('event_crop_temp');
        final tempFile = File('${tempDir.path}/padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        await tempFile.writeAsBytes(paddedBytes);

        // 2. 크롭 다이얼로그 호출 (흰색 패딩 포함 이미지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: tempFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null) {
          // 3. 크롭된 이미지를 200x200으로 리사이즈 (프로필 이미지와 동일)
          final croppedBytes = await croppedFile.readAsBytes();
          final img.Image? imgDecoded = img.decodeImage(croppedBytes);
          final img.Image imgResized = img.copyResize(imgDecoded!, width: 200, height: 200);
          final jpgBytes = img.encodeJpg(imgResized, quality: 80);

          // 4. 영구 저장소에 최종 파일 저장
          final appDocDir = await getApplicationDocumentsDirectory();
          final eventImagesDir = Directory(path.join(appDocDir.path, 'event_images'));
          if (!await eventImagesDir.exists()) {
            await eventImagesDir.create(recursive: true);
          }

          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final fileName = 'event_${timestamp}.jpg';
          final finalFile = File(path.join(eventImagesDir.path, fileName));
          await finalFile.writeAsBytes(jpgBytes);

          setState(() {
            _imagePath = finalFile.path;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('이미지 선택 실패', tag: _tag, error: e);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('이미지 선택에 실패했습니다')),
        );
      }
    }
  }



  /// 날짜 선택 섹션 (항상 표시)
  Widget _buildDateSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 날짜 선택 버튼
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
            border: Border.all(
              color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
              onTap: () {
                // 키보드 숨기기
                FocusScope.of(context).unfocus();
                _showDateRangePicker(context);
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_month,
                      color: AppColors.onboardingPrimary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '행사 기간 선택하기',
                            style: TextStyle(
                              fontSize: ResponsiveHelper.getBodyFontSize(context),
                              fontWeight: FontWeight.w500,
                              color: AppColors.onboardingTextPrimary,
                            ),
                          ),
                          if (_startDate != null && _endDate != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              '${_startDate!.year}.${_startDate!.month.toString().padLeft(2, '0')}.${_startDate!.day.toString().padLeft(2, '0')} - ${_endDate!.year}.${_endDate!.month.toString().padLeft(2, '0')}.${_endDate!.day.toString().padLeft(2, '0')}',
                              style: TextStyle(
                                color: AppColors.onboardingPrimary,
                                fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: AppColors.onboardingPrimary,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 도움말 텍스트
  Widget _buildHelpText(BuildContext context) {
    return Text(
      '행사별로 상품과 판매 데이터를\n독립적으로 관리할 수 있어요',
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
        color: AppColors.onboardingTextTertiary,
        height: 1.4,
      ),
    );
  }

  /// 행사 생성 가능 여부 확인
  bool _canCreateEvent() {
    return _nameController.text.trim().isNotEmpty &&
           _startDate != null &&
           _endDate != null &&
           !_isLoading;
  }

  /// 행사 생성
  Future<void> _createEvent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_startDate == null || _endDate == null) {
      _showErrorToast('행사 기간을 선택해주세요');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      LoggerUtils.methodStart('_createEvent', tag: _tag);

      // 행사 워크스페이스 생성 (통합 워크스페이스 Provider 사용)
      final workspace = await ref.read(unifiedWorkspaceProvider.notifier).createWorkspace(
        name: _nameController.text.trim(),
        startDate: _startDate!,
        endDate: _endDate!,
        setAsCurrent: true,
        imagePath: _imagePath,
        description: _memoController.text.trim().isEmpty ? null : _memoController.text.trim(),
      );

      if (workspace != null) {
        LoggerUtils.logInfo('행사 워크스페이스 생성 성공: ${workspace.name}', tag: _tag);

        if (mounted) {
          // 초기화 작업 시작
          setState(() {
            _isLoading = false;
            _isInitializing = true;
          });

          // 새로운 PostOnboardingInitScreen으로 이동
          LoggerUtils.logInfo('PostOnboardingInitScreen으로 이동', tag: _tag);
          await _navigateToPostOnboardingInit(workspace);
        }
      } else {
        throw Exception('행사 워크스페이스 생성 실패');
      }

      LoggerUtils.methodEnd('_createEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('워크스페이스 생성 실패', tag: _tag, error: e, stackTrace: stackTrace);
      _showErrorToast('워크스페이스 생성에 실패했습니다: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isInitializing = false;
        });
      }
    }
  }

  /// 날짜 범위 선택 다이얼로그 표시
  Future<void> _showDateRangePicker(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: AppColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context) * 1.5),
          ),
          child: Container(
            padding: ResponsiveHelper.getCardPadding(context),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 헤더
                Row(
                  children: [
                    Text(
                      '행사 기간 선택',
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getSubtitleFontSize(context),
                        fontWeight: FontWeight.w600,
                        color: AppColors.onboardingTextPrimary,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close,
                        color: AppColors.onboardingTextSecondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 날짜 범위 선택기
                Container(
                  height: 350,
                  decoration: BoxDecoration(
                    color: AppColors.surfaceVariant,
                    borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
                  ),
                  child: SfDateRangePicker(
                    onSelectionChanged: _onDateRangeSelectionChanged,
                    selectionMode: DateRangePickerSelectionMode.range,
                    initialSelectedRange: _startDate != null && _endDate != null
                        ? PickerDateRange(_startDate, _endDate)
                        : null,
                    minDate: DateTime.now(),
                    maxDate: DateTime.now().add(const Duration(days: 365)),
                    monthViewSettings: const DateRangePickerMonthViewSettings(
                      firstDayOfWeek: 1, // 월요일부터 시작
                      dayFormat: 'EEE',
                      weekNumberStyle: DateRangePickerWeekNumberStyle(),
                    ),
                    selectionColor: AppColors.onboardingPrimary,
                    rangeSelectionColor: AppColors.primaryOverlay,
                    startRangeSelectionColor: AppColors.onboardingPrimary,
                    endRangeSelectionColor: AppColors.onboardingPrimary,
                    todayHighlightColor: AppColors.onboardingAccent,
                    headerStyle: DateRangePickerHeaderStyle(
                      textStyle: TextStyle(
                        color: AppColors.onboardingTextPrimary,
                        fontSize: ResponsiveHelper.getBodyFontSize(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    monthCellStyle: DateRangePickerMonthCellStyle(
                      textStyle: TextStyle(
                        color: AppColors.onboardingTextPrimary,
                        fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                      ),
                      disabledDatesTextStyle: TextStyle(
                        color: AppColors.onboardingTextTertiary,
                        fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // 확인 버튼
                OnboardingComponents.buildPrimaryButton(
                  context: context,
                  text: '확인',
                  onPressed: () {
                    // 키보드 숨기기
                    FocusScope.of(context).unfocus();
                    Navigator.of(context).pop();
                  },
                  icon: Icons.check,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 날짜 범위 선택 변경 처리
  void _onDateRangeSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    if (args.value is PickerDateRange) {
      final PickerDateRange range = args.value;
      setState(() {
        _startDate = range.startDate;
        _endDate = range.endDate ?? range.startDate;
      });
    }
  }



  /// PostOnboardingInitScreen으로 이동
  Future<void> _navigateToPostOnboardingInit(EventWorkspace workspace) async {
    if (!mounted) return;

    // 현재 화면을 PostOnboardingInitScreen으로 교체
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => PostOnboardingInitScreen(
          createdWorkspace: workspace,
          onInitializationComplete: () {
            // 초기화 완료 후 AppWrapper에 알림
            widget.onWorkspaceCreated();
          },
        ),
      ),
    );
  }

  // 기존 복잡한 상태 동기화 로직 제거됨 - PostOnboardingInitScreen에서 처리

  /// 에러 토스트 표시
  void _showErrorToast(String message) {
    if (mounted) {
      ToastUtils.showError(context, message);
    }
  }
}
