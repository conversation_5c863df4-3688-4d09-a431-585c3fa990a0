import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';

import '../../models/product.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/currency_utils.dart';
import '../../utils/device_utils.dart';
import '../../utils/dimens.dart';
import '../../widgets/slider_quantity_widget.dart';


/// 주문 내역 리스트만 표시하는 패널 위젯 (총 합계와 판매 버튼 제외)
class OrderSummaryPanel extends ConsumerWidget {
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final ValueNotifier<Map<int, int>> serviceQuantitiesNotifier;
  final Function(Product) onProductIncrease;
  final Function(Product) onProductDecrease;
  final Function(Product) onServiceIncrease;
  final Function(Product) onServiceDecrease;

  const OrderSummaryPanel({
    super.key,
    required this.productQuantitiesNotifier,
    required this.serviceQuantitiesNotifier,
    required this.onProductIncrease,
    required this.onProductDecrease,
    required this.onServiceIncrease,
    required this.onServiceDecrease,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productState = ref.watch(productNotifierProvider);
    final allProducts = productState.products.where((product) => product.isActive).toList();

    return _buildPanelContent(context, ref, allProducts);
  }

  Widget _buildPanelContent(BuildContext context, WidgetRef ref, List<Product> allProducts) {
    final isTablet = DeviceUtils.isTablet(context);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border.all(
          color: AppColors.onboardingPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(0), // 라운딩 제거
        boxShadow: [
          BoxShadow(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 헤더
          _buildHeader(context, isTablet),
          const Divider(height: 1),

          // 주문 내역 리스트 - 스크롤 가능하도록 수정
          Expanded(
            child: _buildOrderList(context, ref, allProducts, isTablet),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isTablet) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? Dimens.space16 : Dimens.space12,
        vertical: isTablet ? Dimens.space4 : Dimens.space2, // 위아래 패딩을 더 줄임
      ),
      child: Row(
        children: [
          Icon(
            LucideIcons.shoppingCart,
            size: isTablet ? 20 : 18,
            color: AppColors.onboardingPrimary,
          ),
          SizedBox(width: isTablet ? Dimens.space8 : Dimens.space6),
          Text(
            '주문 내역',
            style: TextStyle(
              fontSize: isTablet ? 16 : 14,
              fontWeight: FontWeight.w600,
              color: AppColors.onboardingTextPrimary,
            ),
          ),
          const Spacer(), // 새로고침 버튼을 오른쪽으로 밀기
          // 새로고침 버튼 추가
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: isTablet ? 20 : 18,
              color: AppColors.onboardingPrimary,
            ),
            onPressed: () {
              _refreshOrderData();
            },
            tooltip: '새로고침',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  // 주문 데이터 새로고침
  void _refreshOrderData() {
    // 선택 초기화
    productQuantitiesNotifier.value = {};
    serviceQuantitiesNotifier.value = {};
  }

  // 상품명에 카테고리명을 포함하여 표시명 생성 (판매기록과 동일한 형식)
  String _getProductDisplayName(Product product, WidgetRef ref) {
    try {
      final categories = ref.watch(currentCategoriesProvider);
      final category = categories.firstWhere(
        (cat) => cat.id == product.categoryId,
      );
      return '[${category.name}] ${product.name}';
    } catch (e) {
      // 카테고리를 찾을 수 없는 경우 원본 상품명 사용
      return product.name;
    }
  }

  Widget _buildOrderList(BuildContext context, WidgetRef ref, List<Product> allProducts, bool isTablet) {
    return ValueListenableBuilder<Map<int, int>>(
      valueListenable: productQuantitiesNotifier,
      builder: (context, productQuantities, child) {
        return ValueListenableBuilder<Map<int, int>>(
          valueListenable: serviceQuantitiesNotifier,
          builder: (context, serviceQuantities, child) {
            // 선택된 상품들
            final selectedProducts = productQuantities.entries
                .where((entry) => entry.value > 0)
                .map((entry) {
                  try {
                    return allProducts.firstWhere((p) => p.id == entry.key);
                  } catch (e) {
                    return null;
                  }
                })
                .where((product) => product != null)
                .cast<Product>()
                .toList();

            // 선택된 서비스들 (서비스는 상품과 동일한 Product 모델 사용)
            final selectedServices = serviceQuantities.entries
                .where((entry) => entry.value > 0)
                .map((entry) {
                  try {
                    return allProducts.firstWhere((p) => p.id == entry.key);
                  } catch (e) {
                    return null;
                  }
                })
                .where((product) => product != null)
                .cast<Product>()
                .toList();

            // 상품과 서비스가 모두 비어있으면 빈 상태 표시
            if (selectedProducts.isEmpty && selectedServices.isEmpty) {
              return SizedBox.expand(
                child: _buildEmptyState(context, isTablet),
              );
            }

            final allItems = <Widget>[];

            // 상품 아이템들 추가
            for (final product in selectedProducts) {
              final quantity = productQuantities[product.id!] ?? 0;
              allItems.add(_buildOrderItem(context, ref, product, quantity, isTablet, false));
            }

            // 서비스 아이템들 추가
            for (final service in selectedServices) {
              final quantity = serviceQuantities[service.id!] ?? 0;
              allItems.add(_buildOrderItem(context, ref, service, quantity, isTablet, true));
            }

            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(isTablet ? Dimens.space12 : Dimens.space8),
                child: Column(children: allItems),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isTablet) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? Dimens.space24 : Dimens.space16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.shoppingBag,
              size: isTablet ? 48 : 40,
              color: AppColors.onboardingTextSecondary.withValues(alpha: 0.5),
            ),
            SizedBox(height: isTablet ? Dimens.space12 : Dimens.space8),
            Text(
              '선택된 상품이 없습니다',
              textAlign: TextAlign.center, // 중앙 정렬 추가
              style: TextStyle(
                fontSize: isTablet ? 14 : 12,
                color: AppColors.onboardingTextSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, WidgetRef ref, Product product, int quantity, bool isTablet, bool isService) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? Dimens.space12 : Dimens.space10,
        vertical: isTablet ? Dimens.space4 : Dimens.space2,
      ),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(0),
        border: Border.all(
          color: AppColors.neutral20,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 상품명 (카테고리 포함) 또는 서비스명
          Expanded(
            flex: 3,
            child: Text(
              _getProductDisplayName(product, ref),
              style: TextStyle(
                fontSize: isTablet ? 13 : 11,
                fontWeight: FontWeight.w500,
                color: isService ? AppColors.categoryElectricBlue : AppColors.onSurface,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          SizedBox(width: isTablet ? Dimens.space8 : Dimens.space6),

          // 수량 조절 슬라이더 (더 접근하기 쉽게 앞으로 이동)
          SliderQuantityWidget(
            quantity: quantity,
            maxQuantity: product.quantity,
            isTablet: isTablet,
            isService: isService,
            onQuantityChanged: (newQuantity) {
              if (isService) {
                // 서비스의 경우 직접 수량 설정
                if (newQuantity > quantity) {
                  for (int i = quantity; i < newQuantity; i++) {
                    onServiceIncrease(product);
                  }
                } else if (newQuantity < quantity) {
                  for (int i = quantity; i > newQuantity; i--) {
                    onServiceDecrease(product);
                  }
                }
              } else {
                // 상품의 경우 직접 수량 설정
                if (newQuantity > quantity) {
                  for (int i = quantity; i < newQuantity; i++) {
                    onProductIncrease(product);
                  }
                } else if (newQuantity < quantity) {
                  for (int i = quantity; i > newQuantity; i--) {
                    onProductDecrease(product);
                  }
                }
              }
            },
          ),

          SizedBox(width: isTablet ? Dimens.space8 : Dimens.space6),

          // 상품 가격 (서비스는 서비스로 표시) - 고정 너비로 100만원까지 표시 가능
          Container(
            width: isTablet ? 105 : 85, // 100만원까지 표시할 수 있는 충분한 너비
            child: Text(
              isService ? '서비스' : CurrencyUtils.formatCurrency(product.price.round()),
              style: TextStyle(
                fontSize: isTablet ? 12 : 10,
                color: isService ? AppColors.categoryElectricBlue : Colors.black,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right, // 오른쪽 정렬로 깔끔하게
            ),
          ),
        ],
      ),
    );
  }




}
